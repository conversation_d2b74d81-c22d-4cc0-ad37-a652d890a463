import math
from typing import List, Dict, Any, <PERSON><PERSON>

import networkx as nx
import os
import pickle

from ReadAirportAircraftData import (
    read_in_airport_data,
    initialise_layout_graph,
    build_edge_successors,
)
from airport_functions import get_segment_info
from landmarks_kqpptw import writeSegments_kqpptw, expandSegments_kqpptw
from database import calculateEdgeTimes
from folder_specifier import get_folder

__all__ = [
    "segment_path",
    "calculate_performance",
]


# 模块级分析器缓存：按机场名复用，避免重复读取数据库与建图
ANALYZER_CACHE: Dict[str, "AirportPathAnalyzer"] = {}


def _build_node_edge_map(edges: List[List[Any]]) -> Dict[Tuple[str, str], List[Any]]:
    """快速通过 (start, end) 查找边数据，同时生成反向边。"""
    mapping = {}
    for e in edges:
        mapping[(e[1], e[2])] = e
        mapping[(e[2], e[1])] = [e[0], e[2], e[1], e[3], e[4], e[5]]
    return mapping


class AirportPathAnalyzer:
    def __init__(self, airport_name: str):
        self.airport_name = airport_name.lower()
        (
            self.nodes,
            self.edges,
            self.dbases,
            self.conflicting_edges,
            _,
        ) = read_in_airport_data(self.airport_name)

        self.G_layout: nx.DiGraph = initialise_layout_graph(self.edges)
        self.edges_no_runway = [e for e in self.edges if e[5] != "runway"]
        self.edge_successors = build_edge_successors(self.edges_no_runway)

        # 磁盘缓存路径
        cache_dir = get_folder('cache_path')
        os.makedirs(cache_dir, exist_ok=True)
        segments_cache_file = os.path.join(cache_dir, f"{self.airport_name}_segments_dict.pkl")
        self._perf_cache_file = os.path.join(cache_dir, f"{self.airport_name}_segment_perf_cache.pkl")

        # 优先加载分段字典缓存
        self.segments_dict = None
        try:
            with open(segments_cache_file, 'rb') as f:
                self.segments_dict = pickle.load(f)
        except Exception:
            self.segments_dict = None

        if self.segments_dict is None:
            self.segments_dict = writeSegments_kqpptw(
                self.nodes, self.edges_no_runway, [], self.edge_successors
            )
            try:
                with open(segments_cache_file, 'wb') as f:
                    pickle.dump(self.segments_dict, f, protocol=pickle.HIGHEST_PROTOCOL)
            except Exception:
                pass

        self.nodes_dict = {n[0]: n for n in self.nodes}
        self.edge_lookup = _build_node_edge_map(self.edges)

        # 段性能缓存（相对时间，起点时间为0），磁盘持久化
        self._segment_perf_cache: Dict[Tuple[Any, ...], Tuple[List[List[float]], List[bool], Dict[str, List[List[float]]]]] = {}
        try:
            with open(self._perf_cache_file, 'rb') as f:
                self._segment_perf_cache = pickle.load(f)
        except Exception:
            self._segment_perf_cache = {}

    # ------------------------------------------------------------------
    # 公共接口 1：路径分段
    # ------------------------------------------------------------------
    def segment_path(
        self,
        node_path: List[str],
        weight_class: str,
        aircraft_type: str = "departure",
    ) -> List[Dict[str, Any]]:
        """严格依照原项目的 segments_dict 分段，与 writeSegments_kqpptw 一致。"""
        if len(node_path) < 2:
            raise ValueError("节点列表至少包含 2 个元素。")
        if weight_class not in self.dbases:
            raise ValueError(f"不支持的重量类别: {weight_class}")

        # 将节点序列转换为定向边序列
        edge_path: List[List[Any]] = []
        for i in range(len(node_path) - 1):
            edge = self.edge_lookup.get((node_path[i], node_path[i + 1]))
            if not edge:
                raise ValueError(f"在布局中找不到边: {node_path[i]} -> {node_path[i+1]}")
            edge_path.append(edge)

        segments: List[Dict[str, Any]] = []
        i = 0
        seg_id = 0
        while i < len(edge_path):
            start_node = node_path[i]
            next_node = node_path[i + 1]
            pred_node = node_path[i - 1] if i > 0 else None

            if pred_node is None:
                # 起点无前驱：直接用原库 expandSegments_kqpptw 生成候选，保证一致
                starting_edge = edge_path[i]
                cand_segments, turns30 = expandSegments_kqpptw(
                    starting_edge, self.nodes, self.edges_no_runway, None, self.edge_successors
                )
            else:
                key = f"{start_node}x{next_node}x{pred_node}"
                entry = self.segments_dict.get(key)
                if entry is None:
                    raise ValueError(
                        f"segments_dict 中找不到起点 {start_node}->{next_node} 的分段定义（前驱={pred_node}）。"
                    )
                cand_segments, turns30 = entry
            # 在候选中选取与 path 最长前缀匹配的段
            best_match: List[List[Any]] = []
            for cand in cand_segments:
                if self._segment_matches_path_prefix(cand, edge_path, i):
                    if len(cand) > len(best_match):
                        best_match = cand

            if not best_match:
                # 理论上不应发生，保护性报错确保一致性
                raise ValueError(
                    f"无法在 segments_dict 的候选段中匹配路径前缀，起点 {start_node}->{next_node}（前驱={pred_node}）。"
                )

            seg_dict = self._create_segment_dict(
                best_match,
                seg_id,
                turns30,
                weight_class,
                node_path[0],
                node_path[-1],
                aircraft_type,
            )
            segments.append(seg_dict)
            seg_id += 1
            i += len(best_match)

        return segments

    def _segment_matches_path_prefix(
        self,
        cand_segment: List[List[Any]],
        edge_path: List[List[Any]],
        start_idx: int,
    ) -> bool:
        if start_idx + len(cand_segment) > len(edge_path):
            return False
        for j, e in enumerate(cand_segment):
            ep = edge_path[start_idx + j]
            if not (e[0] == ep[0] and e[1] == ep[1] and e[2] == ep[2]):
                return False
        return True

    # ------------------------------------------------------------------
    def _create_segment_dict(
        self,
        seg_edges: List[List[Any]],
        seg_id: int,
        turns30: List[str],
        weight_class: str,
        start_node: str,
        end_node: str,
        aircraft_type: str,
    ) -> Dict[str, Any]:
        # 确定 successor 与 end_of_decoded_path（与原库的用法对齐）
        if len(seg_edges) > 1:
            successor = seg_edges[-1][2] if seg_edges[-1][1] in seg_edges[-2][1:3] else seg_edges[-1][1]
        else:
            successor = seg_edges[-1][2]
        end_of_decoded_path = seg_edges[0][1]

        # 获取段特征与数据库条目
        (initial_v, end_v, seg_len, seg_type, db_entry, successor, pred_node,) = get_segment_info(
            seg_edges,
            turns30,
            successor,
            end_of_decoded_path,
            weight_class,
            self.dbases,
            seg_edges[0],
            start_node,
            end_node,
            aircraft_type,
        )

        return {
            "segment_id": seg_id,
            "edges": seg_edges,
            "length": seg_len,
            "segment_type": seg_type,
            "initial_speed": initial_v,
            "end_speed": end_v,
            "db_entry": db_entry,
        }

    # ------------------------------------------------------------------
    # 公共接口 2：性能评估
    # ------------------------------------------------------------------
    def calculate_performance(
        self,
        segments: List[Dict[str, Any]],
        speed_profile_choices: List[int],
        weight_class: str,
        start_time: float = 0.0,
        validate_time_windows: bool = True,
        fallback_strategy: str = "error",  # "error", "alternative", "ignore"
    ) -> Dict[str, Any]:
        if len(segments) != len(speed_profile_choices):
            raise ValueError("速度剖面选择数量必须与段数量一致。")
        if weight_class not in self.dbases:
            raise ValueError(f"不支持的重量类别: {weight_class}")

        total_time = total_fuel = total_emission = 0.0
        current_time = start_time
        all_edge_times: Dict[str, List[List[float]]] = {}
        seg_details: List[Dict[str, Any]] = []
        time_window_violations: List[Dict[str, Any]] = []

        for seg_idx, (seg, profile_choice) in enumerate(zip(segments, speed_profile_choices)):
            if not (1 <= profile_choice <= 10):
                raise ValueError("速度剖面编号必须在 1~10 之间。")

            # 重新计算段性能，考虑当前时间
            obj_seg, valid_sols, edge_times = self._compute_segment_perf_with_time(
                seg, weight_class, current_time
            )

            sel_idx = profile_choice - 1

            # 时间窗口约束验证
            if validate_time_windows and not valid_sols[sel_idx]:
                violation_info = {
                    "segment_id": seg["segment_id"],
                    "segment_index": seg_idx,
                    "requested_profile": profile_choice,
                    "violation_time": current_time,
                    "edges": [e[0] for e in seg["edges"]]
                }
                time_window_violations.append(violation_info)

                if fallback_strategy == "error":
                    raise ValueError(
                        f"时间窗口约束冲突：段 {seg['segment_id']} 在时间 {current_time:.2f} "
                        f"使用速度配置文件 {profile_choice} 时违反时间窗口约束。"
                        f"冲突边: {violation_info['edges']}"
                    )
                elif fallback_strategy == "alternative":
                    # 寻找可行的替代速度配置文件
                    alternative_profile = self._find_alternative_profile(valid_sols)
                    if alternative_profile is not None:
                        sel_idx = alternative_profile - 1
                        violation_info["used_alternative"] = alternative_profile
                    else:
                        raise ValueError(
                            f"段 {seg['segment_id']} 在当前时间窗口下没有可行的速度配置文件"
                        )
                # fallback_strategy == "ignore" 时继续使用原配置文件

            objectives = obj_seg[sel_idx]
            total_time += objectives[0]
            total_fuel += objectives[1]
            total_emission += objectives[2]

            # 记录边时间窗
            for eid, t_lists in edge_times.items():
                if len(t_lists) > sel_idx:
                    all_edge_times.setdefault(eid, []).append(t_lists[sel_idx])

            # 更新时间
            current_time += objectives[0]

            seg_details.append(
                {
                    "segment_id": seg["segment_id"],
                    "speed_profile": profile_choice if validate_time_windows and valid_sols[sel_idx]
                                   else violation_info.get("used_alternative", profile_choice),
                    "objectives": objectives,
                    "valid": valid_sols[sel_idx],
                    "time_window_violation": not valid_sols[sel_idx] if validate_time_windows else False,
                }
            )

        return {
            "total_time": total_time,
            "total_fuel": total_fuel,
            "total_emission": total_emission,
            "edge_times": all_edge_times,
            "segment_details": seg_details,
            "time_window_violations": time_window_violations,
            "path_feasible": len(time_window_violations) == 0,
        }

    def _segment_cache_key(self, seg: Dict[str, Any], weight_class: str) -> Tuple[Any, ...]:
        edge_ids = tuple(e[0] for e in seg["edges"])
        return (
            weight_class,
            int(round(float(seg["length"]))),
            seg["segment_type"],
            float(seg["initial_speed"]),
            float(seg["end_speed"]),
            edge_ids,
        )

    def _get_or_compute_segment_perf(
        self, seg: Dict[str, Any], weight_class: str
    ) -> Tuple[List[List[float]], List[bool], Dict[str, List[List[float]]]]:
        key = self._segment_cache_key(seg, weight_class)
        if key in self._segment_perf_cache:
            return self._segment_perf_cache[key]

        obj_seg, _, _, valid_sols, edge_times = calculateEdgeTimes(
            0.0,
            [
                seg["segment_id"],
                seg["initial_speed"],
                seg["end_speed"],
                seg["length"],
                seg["segment_type"],
                0,
            ],
            seg["db_entry"],
            weight_class,
            seg["edges"],
            self.G_layout,
            10,
        )

        # 压缩为相对时间（已是相对0）
        rel_edge_times = {eid: [tw for tw in tws] for eid, tws in edge_times.items()}
        self._segment_perf_cache[key] = (obj_seg, valid_sols, rel_edge_times)
        # 计算后立即持久化，确保下次运行直接命中
        self._persist_segment_perf_cache()
        return self._segment_perf_cache[key]

    def _persist_segment_perf_cache(self) -> None:
        try:
            with open(self._perf_cache_file, 'wb') as f:
                pickle.dump(self._segment_perf_cache, f, protocol=pickle.HIGHEST_PROTOCOL)
        except Exception:
            pass

    def _compute_segment_perf_with_time(
        self, seg: Dict[str, Any], weight_class: str, start_time: float
    ) -> Tuple[List[List[float]], List[bool], Dict[str, List[List[float]]]]:
        """计算段性能，考虑实际开始时间的时间窗口约束"""
        obj_seg, _, _, valid_sols, edge_times = calculateEdgeTimes(
            start_time,  # 使用实际开始时间
            [
                seg["segment_id"],
                seg["initial_speed"],
                seg["end_speed"],
                seg["length"],
                seg["segment_type"],
                0,
            ],
            seg["db_entry"],
            weight_class,
            seg["edges"],
            self.G_layout,
            10,
        )
        return obj_seg, valid_sols, edge_times

    def _find_alternative_profile(self, valid_sols: List[bool]) -> int:
        """寻找可行的替代速度配置文件"""
        for i, is_valid in enumerate(valid_sols):
            if is_valid:
                return i + 1  # 返回1-based索引
        return None

    def validate_time_windows_for_path(
        self,
        segments: List[Dict[str, Any]],
        speed_profile_choices: List[int],
        weight_class: str,
        start_time: float = 0.0
    ) -> Dict[str, Any]:
        """专门用于验证整个路径的时间窗口约束"""
        violations = []
        current_time = start_time

        for seg_idx, (seg, profile_choice) in enumerate(zip(segments, speed_profile_choices)):
            _, valid_sols, _ = self._compute_segment_perf_with_time(seg, weight_class, current_time)

            if not valid_sols[profile_choice - 1]:
                # 分析具体哪些边有冲突
                conflicted_edges = self._analyze_edge_conflicts(seg, weight_class, current_time, profile_choice)
                violations.append({
                    "segment_id": seg["segment_id"],
                    "segment_index": seg_idx,
                    "profile": profile_choice,
                    "time": current_time,
                    "conflicted_edges": conflicted_edges,
                    "alternative_profiles": [i+1 for i, valid in enumerate(valid_sols) if valid]
                })

            # 使用第一个可行配置文件计算时间增量
            feasible_profile = next((i for i, valid in enumerate(valid_sols) if valid), 0)
            obj_seg, _, _ = self._get_or_compute_segment_perf(seg, weight_class)
            current_time += obj_seg[feasible_profile][0]

        return {
            "violations": violations,
            "is_feasible": len(violations) == 0,
            "total_violations": len(violations)
        }

    def _analyze_edge_conflicts(
        self, seg: Dict[str, Any], weight_class: str, start_time: float, profile_choice: int
    ) -> List[Dict[str, Any]]:
        """分析段中具体哪些边存在时间窗口冲突"""
        _, _, edge_times = self._compute_segment_perf_with_time(seg, weight_class, start_time)
        conflicted_edges = []

        for edge in seg["edges"]:
            edge_id = edge[0]
            if edge_id in edge_times and len(edge_times[edge_id]) > profile_choice - 1:
                t_start, t_end = edge_times[edge_id][profile_choice - 1]
                time_windows = self.G_layout[edge[1]][edge[2]]['tw']

                # 检查是否与任何时间窗口冲突
                is_valid = any(t_start >= tw[0] and t_end <= tw[1] for tw in time_windows)
                if not is_valid:
                    conflicted_edges.append({
                        "edge_id": edge_id,
                        "start_node": edge[1],
                        "end_node": edge[2],
                        "required_time": [t_start, t_end],
                        "available_windows": time_windows
                    })

        return conflicted_edges

    # ------------------------------------------------------------------
    # 路径合法性检查功能
    # ------------------------------------------------------------------

    def validate_path_comprehensive(
        self,
        node_path: List[str],
        weight_class: str,
        aircraft_type: str = "departure"
    ) -> Dict[str, Any]:
        """全面的路径合法性检查"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "path_info": {
                "total_nodes": len(node_path),
                "total_edges": len(node_path) - 1 if len(node_path) > 1 else 0,
                "total_length": 0.0
            }
        }

        # 1. 基本路径长度检查
        if len(node_path) < 2:
            validation_result["errors"].append("路径至少需要包含2个节点")
            validation_result["is_valid"] = False
            return validation_result

        # 2. 节点存在性检查
        missing_nodes = self._check_nodes_existence(node_path)
        if missing_nodes:
            validation_result["errors"].append(f"以下节点在机场布局中不存在: {missing_nodes}")
            validation_result["is_valid"] = False

        # 3. 边连通性检查
        connectivity_issues = self._check_path_connectivity(node_path)
        if connectivity_issues:
            validation_result["errors"].extend(connectivity_issues)
            validation_result["is_valid"] = False

        # 4. 边方向性检查
        direction_issues = self._check_edge_directions(node_path)
        if direction_issues:
            validation_result["errors"].extend(direction_issues)
            validation_result["is_valid"] = False

        # 5. 特殊区域访问权限检查
        access_warnings = self._check_special_area_access(node_path, aircraft_type, weight_class)
        if access_warnings:
            validation_result["warnings"].extend(access_warnings)

        # 6. 路径几何特性分析
        if validation_result["is_valid"]:
            path_analysis = self._analyze_path_geometry(node_path)
            validation_result["path_info"].update(path_analysis)

        return validation_result

    def _check_nodes_existence(self, node_path: List[str]) -> List[str]:
        """检查节点是否存在于机场布局中"""
        missing_nodes = []
        for node in node_path:
            if node not in self.nodes_dict:
                missing_nodes.append(node)
        return missing_nodes

    def _check_path_connectivity(self, node_path: List[str]) -> List[str]:
        """检查路径的连通性"""
        connectivity_issues = []
        for i in range(len(node_path) - 1):
            current_node = node_path[i]
            next_node = node_path[i + 1]

            # 检查是否存在直接连接
            if not self.G_layout.has_edge(current_node, next_node):
                connectivity_issues.append(
                    f"节点 {current_node} 和 {next_node} 之间没有直接连接 (位置: {i}->{i+1})"
                )
        return connectivity_issues

    def _check_edge_directions(self, node_path: List[str]) -> List[str]:
        """检查边的方向性是否正确"""
        direction_issues = []
        for i in range(len(node_path) - 1):
            current_node = node_path[i]
            next_node = node_path[i + 1]

            # 获取边信息
            edge_key = (current_node, next_node)
            if edge_key in self.edge_lookup:
                edge = self.edge_lookup[edge_key]
                # 检查边的类型和方向限制
                if edge[5] == 'runway':
                    direction_issues.append(
                        f"路径不应直接穿越跑道边: {current_node} -> {next_node}"
                    )
        return direction_issues

    def _check_special_area_access(
        self, node_path: List[str], aircraft_type: str, weight_class: str
    ) -> List[str]:
        """检查特殊区域的访问权限"""
        warnings = []

        # 检查起点和终点的合理性
        start_node = node_path[0]
        end_node = node_path[-1]

        # 根据飞机类型检查起点终点的合理性
        if aircraft_type == "departure":
            # 出发航班应该从登机口开始
            start_edges = [e for e in self.edges if start_node in e[1:3] and e[5] == 'gate']
            if not start_edges:
                warnings.append(f"出发航班的起点 {start_node} 不是登机口")

            # 出发航班应该在跑道结束
            end_edges = [e for e in self.edges if end_node in e[1:3] and e[5] == 'runway']
            if not end_edges:
                warnings.append(f"出发航班的终点 {end_node} 不是跑道")

        elif aircraft_type == "arrival":
            # 到达航班应该从跑道开始
            start_edges = [e for e in self.edges if start_node in e[1:3] and e[5] == 'runway']
            if not start_edges:
                warnings.append(f"到达航班的起点 {start_node} 不是跑道")

            # 到达航班应该在登机口结束
            end_edges = [e for e in self.edges if end_node in e[1:3] and e[5] == 'gate']
            if not end_edges:
                warnings.append(f"到达航班的终点 {end_node} 不是登机口")

        return warnings

    def _analyze_path_geometry(self, node_path: List[str]) -> Dict[str, Any]:
        """分析路径的几何特性"""
        total_length = 0.0
        turn_count = 0
        edge_types = {"taxiway": 0, "gate": 0, "runway": 0, "other": 0}

        for i in range(len(node_path) - 1):
            edge_key = (node_path[i], node_path[i + 1])
            if edge_key in self.edge_lookup:
                edge = self.edge_lookup[edge_key]
                total_length += float(edge[4])
                edge_type = edge[5] if edge[5] in edge_types else "other"
                edge_types[edge_type] += 1

        # 计算转弯次数（简化版本）
        if len(node_path) >= 3:
            for i in range(len(node_path) - 2):
                # 这里可以添加更复杂的角度计算逻辑
                turn_count += 1  # 简化处理

        return {
            "total_length": total_length,
            "estimated_turns": turn_count,
            "edge_type_distribution": edge_types,
            "average_edge_length": total_length / max(1, len(node_path) - 1)
        }

    # ------------------------------------------------------------------
    # 冲突边检测功能
    # ------------------------------------------------------------------

    def detect_path_conflicts(
        self,
        segments: List[Dict[str, Any]],
        speed_profile_choices: List[int],
        weight_class: str,
        other_aircraft_schedules: List[Dict[str, Any]] = None,
        conflict_threshold: float = 60.0,  # 冲突距离阈值（米）
        start_time: float = 0.0
    ) -> Dict[str, Any]:
        """检测路径中的潜在冲突"""

        # 计算当前路径的边时间占用
        performance_result = self.calculate_performance(
            segments, speed_profile_choices, weight_class, start_time, validate_time_windows=False
        )

        current_path_schedule = self._extract_edge_schedule(
            performance_result["edge_times"], segments
        )

        conflicts = []

        # 1. 自身路径内部冲突检测（同一时间占用冲突边）
        internal_conflicts = self._detect_internal_conflicts(
            current_path_schedule, conflict_threshold
        )
        conflicts.extend(internal_conflicts)

        # 2. 与其他飞机的冲突检测
        if other_aircraft_schedules:
            external_conflicts = self._detect_external_conflicts(
                current_path_schedule, other_aircraft_schedules, conflict_threshold
            )
            conflicts.extend(external_conflicts)

        # 3. 冲突严重性分析
        conflict_analysis = self._analyze_conflict_severity(conflicts)

        return {
            "conflicts": conflicts,
            "conflict_count": len(conflicts),
            "has_conflicts": len(conflicts) > 0,
            "conflict_analysis": conflict_analysis,
            "current_path_schedule": current_path_schedule
        }

    def _extract_edge_schedule(
        self, edge_times: Dict[str, List[List[float]]], segments: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """从边时间信息中提取调度信息"""
        schedule = []
        for edge_id, time_windows in edge_times.items():
            for time_window in time_windows:
                # 找到对应的边信息
                edge_info = None
                for seg in segments:
                    for edge in seg["edges"]:
                        if edge[0] == edge_id:
                            edge_info = edge
                            break
                    if edge_info:
                        break

                if edge_info:
                    schedule.append({
                        "edge_id": edge_id,
                        "start_node": edge_info[1],
                        "end_node": edge_info[2],
                        "start_time": time_window[0],
                        "end_time": time_window[1],
                        "length": float(edge_info[4])
                    })

        return sorted(schedule, key=lambda x: x["start_time"])

    def _detect_internal_conflicts(
        self, path_schedule: List[Dict[str, Any]], conflict_threshold: float
    ) -> List[Dict[str, Any]]:
        """检测路径内部的冲突（理论上不应该发生，但用于验证）"""
        internal_conflicts = []

        for i, edge1 in enumerate(path_schedule):
            for j, edge2 in enumerate(path_schedule[i+1:], i+1):
                # 检查时间重叠
                if self._time_windows_overlap(
                    [edge1["start_time"], edge1["end_time"]],
                    [edge2["start_time"], edge2["end_time"]]
                ):
                    # 检查是否为冲突边
                    if self._are_edges_conflicting(edge1["edge_id"], edge2["edge_id"], conflict_threshold):
                        internal_conflicts.append({
                            "type": "internal",
                            "edge1": edge1,
                            "edge2": edge2,
                            "conflict_distance": self.conflicting_edges.get(edge1["edge_id"], {}).get(edge2["edge_id"], float('inf')),
                            "time_overlap": self._calculate_time_overlap(
                                [edge1["start_time"], edge1["end_time"]],
                                [edge2["start_time"], edge2["end_time"]]
                            )
                        })

        return internal_conflicts

    def _detect_external_conflicts(
        self,
        current_schedule: List[Dict[str, Any]],
        other_schedules: List[Dict[str, Any]],
        conflict_threshold: float
    ) -> List[Dict[str, Any]]:
        """检测与其他飞机的冲突"""
        external_conflicts = []

        for other_aircraft in other_schedules:
            aircraft_id = other_aircraft.get("aircraft_id", "unknown")
            other_edge_schedule = other_aircraft.get("edge_schedule", [])

            for current_edge in current_schedule:
                for other_edge in other_edge_schedule:
                    # 检查时间重叠
                    if self._time_windows_overlap(
                        [current_edge["start_time"], current_edge["end_time"]],
                        [other_edge["start_time"], other_edge["end_time"]]
                    ):
                        # 检查是否为冲突边
                        if self._are_edges_conflicting(
                            current_edge["edge_id"], other_edge["edge_id"], conflict_threshold
                        ):
                            external_conflicts.append({
                                "type": "external",
                                "current_edge": current_edge,
                                "conflicting_aircraft": aircraft_id,
                                "conflicting_edge": other_edge,
                                "conflict_distance": self.conflicting_edges.get(
                                    current_edge["edge_id"], {}
                                ).get(other_edge["edge_id"], float('inf')),
                                "time_overlap": self._calculate_time_overlap(
                                    [current_edge["start_time"], current_edge["end_time"]],
                                    [other_edge["start_time"], other_edge["end_time"]]
                                )
                            })

        return external_conflicts

    def _are_edges_conflicting(self, edge1_id: str, edge2_id: str, threshold: float) -> bool:
        """判断两条边是否冲突"""
        if edge1_id == edge2_id:
            return True  # 同一条边肯定冲突

        # 检查冲突边数据库
        if edge1_id in self.conflicting_edges:
            if edge2_id in self.conflicting_edges[edge1_id]:
                distance = self.conflicting_edges[edge1_id][edge2_id]
                return distance <= threshold

        # 反向检查
        if edge2_id in self.conflicting_edges:
            if edge1_id in self.conflicting_edges[edge2_id]:
                distance = self.conflicting_edges[edge2_id][edge1_id]
                return distance <= threshold

        return False

    def _time_windows_overlap(self, window1: List[float], window2: List[float]) -> bool:
        """检查两个时间窗口是否重叠"""
        return not (window1[1] <= window2[0] or window2[1] <= window1[0])

    def _calculate_time_overlap(self, window1: List[float], window2: List[float]) -> float:
        """计算两个时间窗口的重叠时间"""
        if not self._time_windows_overlap(window1, window2):
            return 0.0

        overlap_start = max(window1[0], window2[0])
        overlap_end = min(window1[1], window2[1])
        return overlap_end - overlap_start

    def _analyze_conflict_severity(self, conflicts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析冲突的严重性"""
        if not conflicts:
            return {"severity": "none", "max_overlap": 0.0, "critical_conflicts": 0}

        max_overlap = max(conflict["time_overlap"] for conflict in conflicts)
        critical_conflicts = sum(1 for conflict in conflicts if conflict["time_overlap"] > 30.0)  # 30秒以上为严重冲突

        severity = "low"
        if critical_conflicts > 0:
            severity = "critical"
        elif max_overlap > 10.0:
            severity = "medium"

        return {
            "severity": severity,
            "max_overlap": max_overlap,
            "critical_conflicts": critical_conflicts,
            "total_conflicts": len(conflicts),
            "average_overlap": sum(conflict["time_overlap"] for conflict in conflicts) / len(conflicts)
        }


# ---------------- 对外简单函数封装 ----------------

def segment_path(
    airport_name: str,
    node_path: List[str],
    weight_class: str,
    aircraft_type: str = "departure",
):
    analyzer = ANALYZER_CACHE.get(airport_name.lower())
    if analyzer is None:
        analyzer = AirportPathAnalyzer(airport_name)
        ANALYZER_CACHE[airport_name.lower()] = analyzer
    return analyzer.segment_path(node_path, weight_class, aircraft_type)


def calculate_performance(
    airport_name: str,
    segments: List[Dict[str, Any]],
    speed_profile_choices: List[int],
    weight_class: str,
    start_time: float = 0.0,
):
    analyzer = ANALYZER_CACHE.get(airport_name.lower())
    if analyzer is None:
        analyzer = AirportPathAnalyzer(airport_name)
        ANALYZER_CACHE[airport_name.lower()] = analyzer
    return analyzer.calculate_performance(
        segments, speed_profile_choices, weight_class, start_time
    )


if __name__ == "__main__":
    # 简单自测示例
    airport = "doh"
    sample_path = [
        "A10",
        "N407",
        "N163",
        "N165",
        "N204",
        "N147",
        "N185",
        "N398",
        "N397",
        "N186",
        "N146",
        "N154",
        "N152",
        "N158",
        "N150",
        "N156",
        "N145",
        "N187",
        "N394",
        "N395",
        "N396",
        "N196",
        "N144",
        "N206",
        "N143",
        "N34",
        "N245",
        "N35",
        "N247",
        "N246",
        "N36",
        "N249",
        "N248",
        "N37",
        "N250",
        "N38",
        "N379",
        "N378",
        "N45",
        "N252",
        "N251",
        "N74",
        "N441",
        "N194",
        "N296",
        "N297",
        "N298",
        "N299",
        "N300",
        "N301",
        "N302",
        "N303",
        "N62",
        "N304",
        "N305",
        "N306",
        "N307",
        "N308",
        "N309",
        "N310",
        "N311",
        "N312",
        "N313",
        "N314",
        "N182",
        "N77",
        "N183",
        "N316",
        "N315",
        "N15",
        "N318",
        "N317",
        "N181",
        "N16",
        "N180",
        "N434",
        "N433",
        "N432",
        "N2",
        "N230",
        "N229",
        "N19",
        "N233",
        "N232",
        "N231",
        "N23",
        "N238",
        "N237",
        "N236",
        "N235",
        "N234",
        "N178",
        "N3",
        "N4",
        "N5",
        "N6",
        "N7",
        "N8",
    ]
    segs = segment_path(airport, sample_path, "medium", "departure")
    print(len(segs))
    for seg in segs:
        print(seg)
    profiles = [1] * len(segs)
    result = calculate_performance(airport, segs, profiles, "medium")
    print(result)