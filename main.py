import math
import random
from typing import List, Dict, Any, Tuple

import networkx as nx
import os
import pickle

from ReadAirportAircraftData import (
    read_in_airport_data,
    initialise_layout_graph,
    build_edge_successors,
)
from airport_functions import get_segment_info
from landmarks_kqpptw import writeSegments_kqpptw, expandSegments_kqpptw
from database import calculateEdgeTimes
from folder_specifier import get_folder

__all__ = [
    "segment_path",
    "calculate_performance_int",
    "calculate_performance",
    "get_segment_profile_counts",
]


# 模块级分析器缓存：按机场名复用，避免重复读取数据库与建图
ANALYZER_CACHE: Dict[str, "AirportPathAnalyzer"] = {}


def _build_node_edge_map(edges: List[List[Any]]) -> Dict[Tuple[str, str], List[Any]]:
    """构建节点对到边的快速查找映射，自动处理双向边"""
    mapping = {}
    for e in edges:
        # 正向边
        mapping[(e[1], e[2])] = e
        # 反向边：交换起终点，保持其他属性不变
        mapping[(e[2], e[1])] = [e[0], e[2], e[1], e[3], e[4], e[5]]
    return mapping


class AirportPathAnalyzer:
    """机场路径分析器：提供路径分段和性能计算功能"""

    def __init__(self, airport_name: str):
        self.airport_name = airport_name.lower()

        # 加载机场基础数据：节点、边、速度配置文件数据库、冲突边信息
        (
            self.nodes,
            self.edges,
            self.dbases,
            self.conflicting_edges,
            _,
        ) = read_in_airport_data(self.airport_name)

        # 构建机场布局图和相关数据结构
        self.G_layout: nx.DiGraph = initialise_layout_graph(self.edges)
        self.edges_no_runway = [e for e in self.edges if e[5] != "runway"]
        self.edge_successors = build_edge_successors(self.edges_no_runway)

        # 设置缓存目录和文件路径
        cache_dir = get_folder('cache_path')
        os.makedirs(cache_dir, exist_ok=True)
        segments_cache_file = os.path.join(cache_dir, f"{self.airport_name}_segments_dict.pkl")
        self._perf_cache_file = os.path.join(cache_dir, f"{self.airport_name}_segment_perf_cache.pkl")

        # 加载或生成分段字典（预计算所有可能的段组合）
        self.segments_dict = None
        try:
            with open(segments_cache_file, 'rb') as f:
                self.segments_dict = pickle.load(f)
        except Exception:
            self.segments_dict = None

        if self.segments_dict is None:
            # 生成分段字典：基于角度和前驱关系预计算所有段
            self.segments_dict = writeSegments_kqpptw(
                self.nodes, self.edges_no_runway, [], self.edge_successors
            )
            # 缓存到磁盘以提高后续加载速度
            try:
                with open(segments_cache_file, 'wb') as f:
                    pickle.dump(self.segments_dict, f, protocol=pickle.HIGHEST_PROTOCOL)
            except Exception:
                pass

        # 构建快速查找数据结构
        self.nodes_dict = {n[0]: n for n in self.nodes}
        self.edge_lookup = _build_node_edge_map(self.edges)

        # 段性能缓存：存储计算过的段性能数据（时间、燃油、排放）
        self._segment_perf_cache: Dict[Tuple[Any, ...], Tuple[List[List[float]], List[bool], Dict[str, List[List[float]]]]] = {}
        try:
            with open(self._perf_cache_file, 'rb') as f:
                self._segment_perf_cache = pickle.load(f)
        except Exception:
            self._segment_perf_cache = {}

    # ------------------------------------------------------------------
    # 功能1：路径分段 - 将完整路径按系统规则分解为段序列
    # ------------------------------------------------------------------
    def segment_path(
        self,
        node_path: List[str],
        weight_class: str,
        aircraft_type: str = "departure",
    ) -> List[Dict[str, Any]]:
        """
        将节点路径分段为符合系统规则的段序列

        Args:
            node_path: 节点序列，如 ['A10', 'N407', 'N163', ...]
            weight_class: 飞机重量类别 ('medium', 'heavy')
            aircraft_type: 飞机类型 ('departure', 'arrival')

        Returns:
            段列表，每个段包含edges、长度、类型、速度等信息
        """
        if len(node_path) < 2:
            raise ValueError("节点列表至少包含 2 个元素。")
        if weight_class not in self.dbases:
            raise ValueError(f"不支持的重量类别: {weight_class}")

        # 将节点序列转换为边序列
        edge_path: List[List[Any]] = []
        for i in range(len(node_path) - 1):
            edge = self.edge_lookup.get((node_path[i], node_path[i + 1]))
            if not edge:
                raise ValueError(f"在布局中找不到边: {node_path[i]} -> {node_path[i+1]}")
            edge_path.append(edge)

        # 贪婪分段：每次选择最长匹配段
        segments: List[Dict[str, Any]] = []
        i = 0
        seg_id = 0
        while i < len(edge_path):
            start_node = node_path[i]
            next_node = node_path[i + 1]
            pred_node = node_path[i - 1] if i > 0 else None

            # 获取候选段：起点无前驱时动态生成，否则从预计算字典查找
            if pred_node is None:
                starting_edge = edge_path[i]
                cand_segments, turns30 = expandSegments_kqpptw(
                    starting_edge, self.nodes, self.edges_no_runway, None, self.edge_successors
                )
            else:
                key = f"{start_node}x{next_node}x{pred_node}"
                entry = self.segments_dict.get(key)
                if entry is None:
                    raise ValueError(
                        f"segments_dict 中找不到起点 {start_node}->{next_node} 的分段定义（前驱={pred_node}）。"
                    )
                cand_segments, turns30 = entry

            # 选择与路径最长匹配的段
            best_match: List[List[Any]] = []
            for cand in cand_segments:
                if self._segment_matches_path_prefix(cand, edge_path, i):
                    if len(cand) > len(best_match):
                        best_match = cand

            if not best_match:
                raise ValueError(
                    f"无法在 segments_dict 的候选段中匹配路径前缀，起点 {start_node}->{next_node}（前驱={pred_node}）。"
                )

            # 创建段字典并添加到结果
            seg_dict = self._create_segment_dict(
                best_match, seg_id, turns30, weight_class,
                node_path[0], node_path[-1], aircraft_type,
            )
            segments.append(seg_dict)
            seg_id += 1
            i += len(best_match)  # 跳过已处理的边

        return segments

    def _segment_matches_path_prefix(
        self,
        cand_segment: List[List[Any]],
        edge_path: List[List[Any]],
        start_idx: int,
    ) -> bool:
        """检查候选段是否与路径前缀匹配"""
        if start_idx + len(cand_segment) > len(edge_path):
            return False
        for j, e in enumerate(cand_segment):
            ep = edge_path[start_idx + j]
            # 比较边ID和起终点
            if not (e[0] == ep[0] and e[1] == ep[1] and e[2] == ep[2]):
                return False
        return True

    def _create_segment_dict(
        self,
        seg_edges: List[List[Any]],
        seg_id: int,
        turns30: List[str],
        weight_class: str,
        start_node: str,
        end_node: str,
        aircraft_type: str,
    ) -> Dict[str, Any]:
        """创建段字典，包含段的所有关键信息"""
        # 确定段的后继节点和解码路径终点（与原系统逻辑对齐）
        if len(seg_edges) > 1:
            successor = seg_edges[-1][2] if seg_edges[-1][1] in seg_edges[-2][1:3] else seg_edges[-1][1]
        else:
            successor = seg_edges[-1][2]
        end_of_decoded_path = seg_edges[0][1]

        # 调用原系统函数获取段特征：速度、类型、数据库条目等
        (initial_v, end_v, seg_len, seg_type, db_entry, successor, pred_node,) = get_segment_info(
            seg_edges, turns30, successor, end_of_decoded_path, weight_class,
            self.dbases, seg_edges[0], start_node, end_node, aircraft_type,
        )

        return {
            "segment_id": seg_id,
            "edges": seg_edges,           # 段包含的边列表
            "length": seg_len,            # 段总长度(米)
            "segment_type": seg_type,     # 段类型：1=直线，2=转弯，3=停机
            "initial_speed": initial_v,   # 起始速度(m/s)
            "end_speed": end_v,          # 结束速度(m/s)
            "db_entry": db_entry,        # 速度配置文件数据库条目
        }

    # ------------------------------------------------------------------
    # 功能2：性能计算 - 计算路径的时间、燃油、排放和边时间窗
    # ------------------------------------------------------------------
    def calculate_performance_int(
        self,
        segments: List[Dict[str, Any]],
        speed_profile_choices: List[int],
        weight_class: str,
        start_time: float = 0.0,
    ) -> Dict[str, Any]:
        """
        计算路径性能指标

        Args:
            segments: 路径分段结果
            speed_profile_choices: 每段的速度配置文件选择(1-10)
            weight_class: 飞机重量类别
            start_time: 路径开始时间(秒)

        Returns:
            包含总时间、总燃油、总排放、边时间窗等信息的字典
        """
        if len(segments) != len(speed_profile_choices):
            raise ValueError("速度剖面选择数量必须与段数量一致。")
        if weight_class not in self.dbases:
            raise ValueError(f"不支持的重量类别: {weight_class}")

        # 初始化累积变量
        total_time = total_fuel = total_emission = 0.0
        current_time = start_time
        all_edge_times: Dict[str, List[List[float]]] = {}
        seg_details: List[Dict[str, Any]] = []

        # 逐段计算性能并累积
        for seg, profile_choice in zip(segments, speed_profile_choices):
            if not (1 <= profile_choice <= 10):
                raise ValueError("速度剖面编号必须在 1~10 之间。")

            # 获取段性能数据（使用缓存的相对时间结果）
            obj_seg_rel, valid_sols, edge_times_rel = self._get_or_compute_segment_perf(seg, weight_class)

            # 提取选定速度配置文件的性能指标
            sel_idx = profile_choice - 1
            objectives = obj_seg_rel[sel_idx]  # [时间, 燃油, 排放]
            total_time += objectives[0]        # 累积总滑行时间(秒)
            total_fuel += objectives[1]        # 累积总燃油消耗(kg)
            total_emission += objectives[2]    # 累积总污染排放(kg)

            # 计算边的绝对时间窗（相对时间 + 当前全局时间）
            for eid, t_lists in edge_times_rel.items():
                if len(t_lists) > sel_idx:
                    t0, t1 = t_lists[sel_idx]
                    all_edge_times.setdefault(eid, []).append([t0 + current_time, t1 + current_time])

            # 更新全局时间指针
            current_time += objectives[0]

            # 记录段详细信息
            seg_details.append({
                "segment_id": seg["segment_id"],
                "speed_profile": profile_choice,
                "objectives": objectives,
                "valid": valid_sols[sel_idx],  # 时间窗约束是否满足
            })

        return {
            "total_time": total_time,          # 总滑行时间(秒)
            "total_fuel": total_fuel,          # 总燃油消耗(kg)
            "edge_times": all_edge_times,      # 各边的时间窗 {edge_id: [[start, end]]}
        }

    def _segment_cache_key(self, seg: Dict[str, Any], weight_class: str) -> Tuple[Any, ...]:
        edge_ids = tuple(e[0] for e in seg["edges"])
        return (
            weight_class,
            int(round(float(seg["length"]))),
            seg["segment_type"],
            float(seg["initial_speed"]),
            float(seg["end_speed"]),
            edge_ids,
        )

    def _get_or_compute_segment_perf(
        self, seg: Dict[str, Any], weight_class: str
    ) -> Tuple[List[List[float]], List[bool], Dict[str, List[List[float]]]]:
        key = self._segment_cache_key(seg, weight_class)
        if key in self._segment_perf_cache:
            return self._segment_perf_cache[key]

        obj_seg, _, _, valid_sols, edge_times = calculateEdgeTimes(
            0.0,
            [
                seg["segment_id"],
                seg["initial_speed"],
                seg["end_speed"],
                seg["length"],
                seg["segment_type"],
                0,
            ],
            seg["db_entry"],
            weight_class,
            seg["edges"],
            self.G_layout,
            10,
        )

        # 压缩为相对时间（已是相对0）
        rel_edge_times = {eid: [tw for tw in tws] for eid, tws in edge_times.items()}
        self._segment_perf_cache[key] = (obj_seg, valid_sols, rel_edge_times)
        # 计算后立即持久化，确保下次运行直接命中
        self._persist_segment_perf_cache()
        return self._segment_perf_cache[key]

    def _persist_segment_perf_cache(self) -> None:
        """将段性能缓存持久化到磁盘"""
        try:
            with open(self._perf_cache_file, 'wb') as f:
                pickle.dump(self._segment_perf_cache, f, protocol=pickle.HIGHEST_PROTOCOL)
        except Exception:
            pass

    # ------------------------------------------------------------------
    # 扩展功能：支持小数参数的性能计算
    # ------------------------------------------------------------------
    def get_segment_profile_counts(self, segments: List[Dict[str, Any]], weight_class: str) -> List[int]:
        """
        获取每个段的可用速度配置文件数量

        Args:
            segments: 路径分段结果
            weight_class: 飞机重量类别

        Returns:
            每个段的profile数量列表
        """
        profile_counts = []

        for seg in segments:
            if seg["segment_type"] == 2:  # 转弯段
                # 转弯段只有固定的恒速profile，实际上没有选择空间
                profile_counts.append(1)
            else:
                # 直线段、起飞段、停机段：从数据库条目长度计算
                db_entry = seg["db_entry"]
                if len(db_entry) == 0:
                    # 数据库条目为空的特殊情况
                    profile_counts.append(1)
                else:
                    # 标准情况：每10个数据对应1个profile的3个目标值
                    profile_count = max(1, len(db_entry) // 10)
                    profile_counts.append(profile_count)

        return profile_counts

    def calculate_performance(
        self,
        segments: List[Dict[str, Any]],
        decimal_speed_profiles: List[float],
        weight_class: str,
        start_time: float = 0.0,
    ) -> Dict[str, Any]:
        """
        使用小数参数的性能计算函数

        Args:
            segments: 路径分段结果
            decimal_speed_profiles: 小数参数列表(0.0-1.0)，表示在可用profile中的相对位置
                                  0.0 = 第1个profile (通常最省时)
                                  1.0 = 最后一个profile (通常最省油/环保)
            weight_class: 飞机重量类别
            start_time: 路径开始时间(秒)

        Returns:
            包含总时间、总燃油、总排放、边时间窗等信息的字典
        """
        if len(segments) != len(decimal_speed_profiles):
            raise ValueError("小数参数数量必须与段数量一致。")

        # 获取每个段的profile数量
        profile_counts = self.get_segment_profile_counts(segments, weight_class)

        # 将小数参数映射为整数profile选择
        integer_choices = []
        for i, (decimal_param, profile_count) in enumerate(zip(decimal_speed_profiles, profile_counts)):
            # 参数验证
            if not (0.0 <= decimal_param <= 1.0):
                raise ValueError(f"段{i}的小数参数{decimal_param}超出范围[0.0, 1.0]")

            if segments[i]["segment_type"] == 2:  # 转弯段特殊处理
                # 转弯段只有一个固定选择，忽略小数参数
                profile_choice = 1
            else:
                # 安全映射：确保边界值正确处理
                if decimal_param == 0.0:
                    profile_choice = 1  # 第一个profile
                elif decimal_param == 1.0:
                    profile_choice = profile_count  # 最后一个profile
                else:
                    # 线性映射到1-profile_count范围，使用四舍五入确保均匀分布
                    profile_choice = max(1, min(profile_count,
                                              round(decimal_param * (profile_count - 1)) + 1))

            integer_choices.append(profile_choice)
        print(f"各段选择的profile:{integer_choices}")

        # 调用原有的整数参数性能计算函数
        return self.calculate_performance_int(segments, integer_choices, weight_class, start_time)


# ---------------- 对外简单函数封装 ----------------

def segment_path(
    airport_name: str,
    node_path: List[str],
    weight_class: str,
    aircraft_type: str = "departure",
):
    analyzer = ANALYZER_CACHE.get(airport_name.lower())
    if analyzer is None:
        analyzer = AirportPathAnalyzer(airport_name)
        ANALYZER_CACHE[airport_name.lower()] = analyzer
    return analyzer.segment_path(node_path, weight_class, aircraft_type)


def calculate_performance_int(
    airport_name: str,
    segments: List[Dict[str, Any]],
    speed_profile_choices: List[int],
    weight_class: str,
    start_time: float = 0.0,
):
    """
    性能计算函数 - 使用整数参数指定速度配置文件

    Args:
        airport_name: 机场名称
        segments: 路径分段结果
        speed_profile_choices: 每段的速度配置文件选择(1-10)
        weight_class: 飞机重量类别
        start_time: 路径开始时间(秒)

    Returns:
        包含总时间、总燃油、总排放、边时间窗等信息的字典
    """
    analyzer = ANALYZER_CACHE.get(airport_name.lower())
    if analyzer is None:
        analyzer = AirportPathAnalyzer(airport_name)
        ANALYZER_CACHE[airport_name.lower()] = analyzer
    return analyzer.calculate_performance_int(
        segments, speed_profile_choices, weight_class, start_time
    )


def calculate_performance(
    airport_name: str,
    segments: List[Dict[str, Any]],
    decimal_speed_profiles: List[float],
    weight_class: str,
    start_time: float = 0.0,
):
    """
    性能计算函数 - 使用小数参数指定速度配置文件

    Args:
        airport_name: 机场名称
        segments: 路径分段结果
        decimal_speed_profiles: 小数参数列表(0.0-1.0)
                              0.0 = 最快profile, 1.0 = 最省油/环保profile
        weight_class: 飞机重量类别
        start_time: 路径开始时间(秒)

    Returns:
        包含总时间、总燃油、总排放、边时间窗等信息的字典
    """
    analyzer = ANALYZER_CACHE.get(airport_name.lower())
    if analyzer is None:
        analyzer = AirportPathAnalyzer(airport_name)
        ANALYZER_CACHE[airport_name.lower()] = analyzer
    return analyzer.calculate_performance(
        segments, decimal_speed_profiles, weight_class, start_time
    )


def get_segment_profile_counts(
    airport_name: str,
    segments: List[Dict[str, Any]],
    weight_class: str,
) -> List[int]:
    """
    获取每个段的可用速度配置文件数量

    Args:
        airport_name: 机场名称
        segments: 路径分段结果
        weight_class: 飞机重量类别

    Returns:
        每个段的profile数量列表
    """
    analyzer = ANALYZER_CACHE.get(airport_name.lower())
    if analyzer is None:
        analyzer = AirportPathAnalyzer(airport_name)
        ANALYZER_CACHE[airport_name.lower()] = analyzer
    return analyzer.get_segment_profile_counts(segments, weight_class)


if __name__ == "__main__":
    """
    示例代码：演示两个核心功能的使用方法
    """
    # 测试参数
    airport = "doh"  # 多哈国际机场
    weight_class = "medium"  # 中型飞机
    aircraft_type = "departure"  # 出发航班

    # 示例路径：从登机口A10到跑道的完整滑行路径
    sample_path = [
        "A10", "N407", "N163", "N165", "N204", "N147", "N185", "N398", "N397", "N186",
        "N146", "N154", "N152", "N158", "N150", "N156", "N145", "N187", "N394", "N395",
        "N396", "N196", "N144", "N206", "N143", "N34", "N245", "N35", "N247", "N246",
        "N36", "N249", "N248", "N37", "N250", "N38", "N379", "N378", "N45", "N252",
        "N251", "N74", "N441", "N194", "N296", "N297", "N298", "N299", "N300", "N301",
        "N302", "N303", "N62", "N304", "N305", "N306", "N307", "N308", "N309", "N310",
        "N311", "N312", "N313", "N314", "N182", "N77", "N183", "N316", "N315", "N15",
        "N318", "N317", "N181", "N16", "N180", "N434", "N433", "N432", "N2", "N230",
        "N229", "N19", "N233", "N232", "N231", "N23", "N238", "N237", "N236", "N235",
        "N234", "N178", "N3", "N4", "N5", "N6", "N7", "N8",
    ]

    # 功能1：路径分段
    print("=== 功能1：路径分段 ===")
    segments = segment_path(airport, sample_path, weight_class, aircraft_type)
    print(f"路径被分为 {len(segments)} 个段")
    for i, seg in enumerate(segments):
        print(f"段{i}: {seg}")

    # 功能2：性能计算（所有段使用使用小数参数）
    print("\n=== 功能2：性能计算（小数参数）===")

    # 获取每个段的profile数量
    profile_counts = get_segment_profile_counts(airport, segments, weight_class)
    print(f"各段profile数量: {profile_counts}")

    # 使用小数参数：0.0=最快, 1.0=最省油
    decimal_profiles = [random.random() for _ in range(len(segments))]

    performance = calculate_performance(
        airport, segments, decimal_profiles, weight_class
    )

    print(f"小数参数: {decimal_profiles[:10]}... (前10个)")
    print(f"总滑行时间: {performance['total_time']} 秒")
    print(f"总燃油消耗: {performance['total_fuel']} kg")


    # 显示前几个边的时间窗
    print("\n前5个边的时间窗:")
    for i, (edge_id, time_windows) in enumerate(performance['edge_times'].items()):
        if i >= 5:
            break
        for tw in time_windows:
            print(f"  边{edge_id}: [{tw[0]:.1f}, {tw[1]:.1f}] 秒")
