import math
from typing import List, Dict, Any, <PERSON><PERSON>

import networkx as nx
import os
import pickle

from ReadAirportAircraftData import (
    read_in_airport_data,
    initialise_layout_graph,
    build_edge_successors,
)
from airport_functions import get_segment_info
from landmarks_kqpptw import writeSegments_kqpptw, expandSegments_kqpptw
from database import calculateEdgeTimes
from folder_specifier import get_folder

__all__ = [
    "segment_path",
    "calculate_performance",
]


# 模块级分析器缓存：按机场名复用，避免重复读取数据库与建图
ANALYZER_CACHE: Dict[str, "AirportPathAnalyzer"] = {}


def _build_node_edge_map(edges: List[List[Any]]) -> Dict[Tuple[str, str], List[Any]]:
    """快速通过 (start, end) 查找边数据，同时生成反向边。"""
    mapping = {}
    for e in edges:
        mapping[(e[1], e[2])] = e
        mapping[(e[2], e[1])] = [e[0], e[2], e[1], e[3], e[4], e[5]]
    return mapping


class AirportPathAnalyzer:
    def __init__(self, airport_name: str):
        self.airport_name = airport_name.lower()
        (
            self.nodes,
            self.edges,
            self.dbases,
            self.conflicting_edges,
            _,
        ) = read_in_airport_data(self.airport_name)

        self.G_layout: nx.DiGraph = initialise_layout_graph(self.edges)
        self.edges_no_runway = [e for e in self.edges if e[5] != "runway"]
        self.edge_successors = build_edge_successors(self.edges_no_runway)

        # 磁盘缓存路径
        cache_dir = get_folder('cache_path')
        os.makedirs(cache_dir, exist_ok=True)
        segments_cache_file = os.path.join(cache_dir, f"{self.airport_name}_segments_dict.pkl")
        self._perf_cache_file = os.path.join(cache_dir, f"{self.airport_name}_segment_perf_cache.pkl")

        # 优先加载分段字典缓存
        self.segments_dict = None
        try:
            with open(segments_cache_file, 'rb') as f:
                self.segments_dict = pickle.load(f)
        except Exception:
            self.segments_dict = None

        if self.segments_dict is None:
            self.segments_dict = writeSegments_kqpptw(
                self.nodes, self.edges_no_runway, [], self.edge_successors
            )
            try:
                with open(segments_cache_file, 'wb') as f:
                    pickle.dump(self.segments_dict, f, protocol=pickle.HIGHEST_PROTOCOL)
            except Exception:
                pass

        self.nodes_dict = {n[0]: n for n in self.nodes}
        self.edge_lookup = _build_node_edge_map(self.edges)

        # 段性能缓存（相对时间，起点时间为0），磁盘持久化
        self._segment_perf_cache: Dict[Tuple[Any, ...], Tuple[List[List[float]], List[bool], Dict[str, List[List[float]]]]] = {}
        try:
            with open(self._perf_cache_file, 'rb') as f:
                self._segment_perf_cache = pickle.load(f)
        except Exception:
            self._segment_perf_cache = {}

    # ------------------------------------------------------------------
    # 公共接口 1：路径分段
    # ------------------------------------------------------------------
    def segment_path(
        self,
        node_path: List[str],
        weight_class: str,
        aircraft_type: str = "departure",
    ) -> List[Dict[str, Any]]:
        """严格依照原项目的 segments_dict 分段，与 writeSegments_kqpptw 一致。"""
        if len(node_path) < 2:
            raise ValueError("节点列表至少包含 2 个元素。")
        if weight_class not in self.dbases:
            raise ValueError(f"不支持的重量类别: {weight_class}")

        # 将节点序列转换为定向边序列
        edge_path: List[List[Any]] = []
        for i in range(len(node_path) - 1):
            edge = self.edge_lookup.get((node_path[i], node_path[i + 1]))
            if not edge:
                raise ValueError(f"在布局中找不到边: {node_path[i]} -> {node_path[i+1]}")
            edge_path.append(edge)

        segments: List[Dict[str, Any]] = []
        i = 0
        seg_id = 0
        while i < len(edge_path):
            start_node = node_path[i]
            next_node = node_path[i + 1]
            pred_node = node_path[i - 1] if i > 0 else None

            if pred_node is None:
                # 起点无前驱：直接用原库 expandSegments_kqpptw 生成候选，保证一致
                starting_edge = edge_path[i]
                cand_segments, turns30 = expandSegments_kqpptw(
                    starting_edge, self.nodes, self.edges_no_runway, None, self.edge_successors
                )
            else:
                key = f"{start_node}x{next_node}x{pred_node}"
                entry = self.segments_dict.get(key)
                if entry is None:
                    raise ValueError(
                        f"segments_dict 中找不到起点 {start_node}->{next_node} 的分段定义（前驱={pred_node}）。"
                    )
                cand_segments, turns30 = entry
            # 在候选中选取与 path 最长前缀匹配的段
            best_match: List[List[Any]] = []
            for cand in cand_segments:
                if self._segment_matches_path_prefix(cand, edge_path, i):
                    if len(cand) > len(best_match):
                        best_match = cand

            if not best_match:
                # 理论上不应发生，保护性报错确保一致性
                raise ValueError(
                    f"无法在 segments_dict 的候选段中匹配路径前缀，起点 {start_node}->{next_node}（前驱={pred_node}）。"
                )

            seg_dict = self._create_segment_dict(
                best_match,
                seg_id,
                turns30,
                weight_class,
                node_path[0],
                node_path[-1],
                aircraft_type,
            )
            segments.append(seg_dict)
            seg_id += 1
            i += len(best_match)

        return segments

    def _segment_matches_path_prefix(
        self,
        cand_segment: List[List[Any]],
        edge_path: List[List[Any]],
        start_idx: int,
    ) -> bool:
        if start_idx + len(cand_segment) > len(edge_path):
            return False
        for j, e in enumerate(cand_segment):
            ep = edge_path[start_idx + j]
            if not (e[0] == ep[0] and e[1] == ep[1] and e[2] == ep[2]):
                return False
        return True

    # ------------------------------------------------------------------
    def _create_segment_dict(
        self,
        seg_edges: List[List[Any]],
        seg_id: int,
        turns30: List[str],
        weight_class: str,
        start_node: str,
        end_node: str,
        aircraft_type: str,
    ) -> Dict[str, Any]:
        # 确定 successor 与 end_of_decoded_path（与原库的用法对齐）
        if len(seg_edges) > 1:
            successor = seg_edges[-1][2] if seg_edges[-1][1] in seg_edges[-2][1:3] else seg_edges[-1][1]
        else:
            successor = seg_edges[-1][2]
        end_of_decoded_path = seg_edges[0][1]

        # 获取段特征与数据库条目
        (initial_v, end_v, seg_len, seg_type, db_entry, successor, pred_node,) = get_segment_info(
            seg_edges,
            turns30,
            successor,
            end_of_decoded_path,
            weight_class,
            self.dbases,
            seg_edges[0],
            start_node,
            end_node,
            aircraft_type,
        )

        return {
            "segment_id": seg_id,
            "edges": seg_edges,
            "length": seg_len,
            "segment_type": seg_type,
            "initial_speed": initial_v,
            "end_speed": end_v,
            "db_entry": db_entry,
        }

    # ------------------------------------------------------------------
    # 公共接口 2：性能评估
    # ------------------------------------------------------------------
    def calculate_performance(
        self,
        segments: List[Dict[str, Any]],
        speed_profile_choices: List[int],
        weight_class: str,
        start_time: float = 0.0,
    ) -> Dict[str, Any]:
        if len(segments) != len(speed_profile_choices):
            raise ValueError("速度剖面选择数量必须与段数量一致。")
        if weight_class not in self.dbases:
            raise ValueError(f"不支持的重量类别: {weight_class}")

        total_time = total_fuel = total_emission = 0.0
        current_time = start_time
        all_edge_times: Dict[str, List[List[float]]] = {}
        seg_details: List[Dict[str, Any]] = []

        for seg, profile_choice in zip(segments, speed_profile_choices):
            if not (1 <= profile_choice <= 10):
                raise ValueError("速度剖面编号必须在 1~10 之间。")

            # 取缓存的相对结果（起点时间=0），再平移到 current_time
            obj_seg_rel, valid_sols, edge_times_rel = self._get_or_compute_segment_perf(seg, weight_class)

            sel_idx = profile_choice - 1
            objectives = obj_seg_rel[sel_idx]
            total_time += objectives[0]
            total_fuel += objectives[1]
            total_emission += objectives[2]

            # 记录边时间窗（平移）
            for eid, t_lists in edge_times_rel.items():
                if len(t_lists) > sel_idx:
                    t0, t1 = t_lists[sel_idx]
                    all_edge_times.setdefault(eid, []).append([t0 + current_time, t1 + current_time])

            # 更新时间（按段用时累加更稳健）
            current_time += objectives[0]

            seg_details.append(
                {
                    "segment_id": seg["segment_id"],
                    "speed_profile": profile_choice,
                    "objectives": objectives,
                    "valid": valid_sols[sel_idx],
                }
            )

        return {
            "total_time": total_time,
            "total_fuel": total_fuel,
            "edge_times": all_edge_times,
        }

    def _segment_cache_key(self, seg: Dict[str, Any], weight_class: str) -> Tuple[Any, ...]:
        edge_ids = tuple(e[0] for e in seg["edges"])
        return (
            weight_class,
            int(round(float(seg["length"]))),
            seg["segment_type"],
            float(seg["initial_speed"]),
            float(seg["end_speed"]),
            edge_ids,
        )

    def _get_or_compute_segment_perf(
        self, seg: Dict[str, Any], weight_class: str
    ) -> Tuple[List[List[float]], List[bool], Dict[str, List[List[float]]]]:
        key = self._segment_cache_key(seg, weight_class)
        if key in self._segment_perf_cache:
            return self._segment_perf_cache[key]

        obj_seg, _, _, valid_sols, edge_times = calculateEdgeTimes(
            0.0,
            [
                seg["segment_id"],
                seg["initial_speed"],
                seg["end_speed"],
                seg["length"],
                seg["segment_type"],
                0,
            ],
            seg["db_entry"],
            weight_class,
            seg["edges"],
            self.G_layout,
            10,
        )

        # 压缩为相对时间（已是相对0）
        rel_edge_times = {eid: [tw for tw in tws] for eid, tws in edge_times.items()}
        self._segment_perf_cache[key] = (obj_seg, valid_sols, rel_edge_times)
        # 计算后立即持久化，确保下次运行直接命中
        self._persist_segment_perf_cache()
        return self._segment_perf_cache[key]

    def _persist_segment_perf_cache(self) -> None:
        try:
            with open(self._perf_cache_file, 'wb') as f:
                pickle.dump(self._segment_perf_cache, f, protocol=pickle.HIGHEST_PROTOCOL)
        except Exception:
            pass


# ---------------- 对外简单函数封装 ----------------

def segment_path(
    airport_name: str,
    node_path: List[str],
    weight_class: str,
    aircraft_type: str = "departure",
):
    analyzer = ANALYZER_CACHE.get(airport_name.lower())
    if analyzer is None:
        analyzer = AirportPathAnalyzer(airport_name)
        ANALYZER_CACHE[airport_name.lower()] = analyzer
    return analyzer.segment_path(node_path, weight_class, aircraft_type)


def calculate_performance(
    airport_name: str,
    segments: List[Dict[str, Any]],
    speed_profile_choices: List[int],
    weight_class: str,
    start_time: float = 0.0,
):
    analyzer = ANALYZER_CACHE.get(airport_name.lower())
    if analyzer is None:
        analyzer = AirportPathAnalyzer(airport_name)
        ANALYZER_CACHE[airport_name.lower()] = analyzer
    return analyzer.calculate_performance(
        segments, speed_profile_choices, weight_class, start_time
    )


if __name__ == "__main__":
    # 简单自测示例
    airport = "doh"
    sample_path = [
        "A10",
        "N407",
        "N163",
        "N165",
        "N204",
        "N147",
        "N185",
        "N398",
        "N397",
        "N186",
        "N146",
        "N154",
        "N152",
        "N158",
        "N150",
        "N156",
        "N145",
        "N187",
        "N394",
        "N395",
        "N396",
        "N196",
        "N144",
        "N206",
        "N143",
        "N34",
        "N245",
        "N35",
        "N247",
        "N246",
        "N36",
        "N249",
        "N248",
        "N37",
        "N250",
        "N38",
        "N379",
        "N378",
        "N45",
        "N252",
        "N251",
        "N74",
        "N441",
        "N194",
        "N296",
        "N297",
        "N298",
        "N299",
        "N300",
        "N301",
        "N302",
        "N303",
        "N62",
        "N304",
        "N305",
        "N306",
        "N307",
        "N308",
        "N309",
        "N310",
        "N311",
        "N312",
        "N313",
        "N314",
        "N182",
        "N77",
        "N183",
        "N316",
        "N315",
        "N15",
        "N318",
        "N317",
        "N181",
        "N16",
        "N180",
        "N434",
        "N433",
        "N432",
        "N2",
        "N230",
        "N229",
        "N19",
        "N233",
        "N232",
        "N231",
        "N23",
        "N238",
        "N237",
        "N236",
        "N235",
        "N234",
        "N178",
        "N3",
        "N4",
        "N5",
        "N6",
        "N7",
        "N8",
    ]
    segs = segment_path(airport, sample_path, "medium", "departure")
    print(len(segs))
    for seg in segs:
        print(seg)
    profiles = [1] * len(segs)
    result = calculate_performance(airport, segs, profiles, "medium")
    print(result)